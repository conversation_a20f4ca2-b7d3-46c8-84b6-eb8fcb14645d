/* Use this task as a playground to run stuff using any app logic.
You can also just copy paste to another file to keep track of previous run tasks */
import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import { LaFourchettePlatformInsights } from ':modules/platform-insights/platforms/lafourchette/use-cases';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import ':plugins/db';

@singleton()
class DefaultTask {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _platformInsightsRepository: PlatformInsightsRepository
    ) {}

    async execute() {
        const laFourchetteInsights = new LaFourchettePlatformInsights(this._platformsRepository, this._platformInsightsRepository);

        // Replace with a real restaurant ID that has LaFourchette platform
        const testRestaurantId = '651444606d6bef2863015fbc';

        try {
            console.log('Testing fetchTodayRating...');
            const rating = await laFourchetteInsights.fetchTodayRating(testRestaurantId);
            console.log('fetchTodayRating result:', rating);
        } catch (error: any) {
            console.error('fetchTodayRating error:', error.message);
        }
    }
}

const task = container.resolve(DefaultTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
