import axios, { AxiosRequestConfig } from 'axios';
import { autoInjectable } from 'tsyringe';
import UserAgent from 'user-agents';

import { ReadPreferenceMode, toDbId } from '@malou-io/package-models';
import {
    AggregationTimeScale,
    errorReplacer,
    FoundStatusOnPlatform,
    isNotNil,
    MalouErrorCode,
    MalouMetric,
    PlatformKey,
    StoredInDBInsightsMetric,
} from '@malou-io/package-utils';

// import { Config } from ':config';
import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { InsightsAggregator } from ':modules/platform-insights/platform-insights.aggregators';
import { platformRatingsFetchCounter } from ':modules/platform-insights/platform-insights.metrics';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import {
    DailyValue,
    MetricToDataValues,
    PlatformInsightUseCase,
    TimeScaleToMetricToDataValues,
} from ':modules/platform-insights/platform-insights.types';
import PlatformsRepository from ':modules/platforms/platforms.repository';

@autoInjectable()
export class LaFourchettePlatformInsights implements PlatformInsightUseCase {
    private readonly _proxy = {
        protocol: 'http',
        host: Config.services.brightData.proxyHost as string,
        port: Config.services.brightData.proxyPort as number,
        auth: {
            username: Config.services.brightData.residentialProxyUsername as string,
            password: Config.services.brightData.residentialProxyPassword as string,
        },
    };

    constructor(
        private _platformsRepository: PlatformsRepository,
        private _platformInsightsRepository: PlatformInsightsRepository
    ) {}

    getInsightsAggregated = async (
        restaurantId: string,
        metrics: MalouMetric[],
        aggregators: AggregationTimeScale[],
        filters
    ): Promise<TimeScaleToMetricToDataValues> => {
        let platform;
        try {
            platform = await this._platformsRepository.findOne({
                filter: { restaurantId, key: PlatformKey.LAFOURCHETTE },
                options: { lean: true },
            });
            if (!platform) {
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    metadata: { restaurantId, platformKey: PlatformKey.LAFOURCHETTE },
                });
            }
        } catch (err) {
            logger.warn('[ERROR_LAFOURCHETTE_PLATFORM]', err);
            return { error: true, message: JSON.stringify(err, errorReplacer) };
        }

        const { startDate, endDate } = filters;
        const insightsByDay: MetricToDataValues<DailyValue> = await this._getInsightsByDay(platform.socialId, metrics, startDate, endDate);
        if ('error' in insightsByDay) {
            return insightsByDay;
        }
        return new InsightsAggregator().aggregateInsights(insightsByDay, aggregators, startDate, endDate);
    };

    private _getInsightsByDay = async (
        pageId: string,
        metrics: MalouMetric[],
        startDate: Date,
        endDate: Date
    ): Promise<MetricToDataValues<DailyValue>> => {
        const insightsByDay: MetricToDataValues<DailyValue> = {};

        if (metrics.includes(MalouMetric.PLATFORM_RATING)) {
            insightsByDay[MalouMetric.PLATFORM_RATING] = await this._getPlatformRatingByDay(pageId, startDate, endDate);
        }

        if (!Object.keys(insightsByDay).length) {
            return { error: true, message: MalouErrorCode.INSIGHTS_NOT_FOUND };
        }

        return insightsByDay;
    };

    private _getPlatformRatingByDay = async (pageId: string, startDate: Date, endDate: Date): Promise<DailyValue[]> => {
        const ratingInsights = await this._platformInsightsRepository.find({
            filter: {
                metric: StoredInDBInsightsMetric.PLATFORM_RATING,
                socialId: pageId,
                platformKey: PlatformKey.LAFOURCHETTE,
                createdAt: { $gte: startDate, $lte: endDate },
            },
            options: { sort: { createdAt: -1 }, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED },
        });
        return ratingInsights.map((f) => (f.value ? { date: new Date(f.year, f.month, f.day), value: f.value } : null)).filter(isNotNil);
    };

    fetchTodayRating = async (restaurantId: string): Promise<number | null | undefined> => {
        const platform = await this._platformsRepository.findOne({
            filter: {
                restaurantId: toDbId(restaurantId),
                key: PlatformKey.LAFOURCHETTE,
                foundStatusOnPlatform: { $ne: FoundStatusOnPlatform.NOT_FOUND },
            },
            options: { lean: true },
        });

        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: { restaurantId, platformKey: PlatformKey.LAFOURCHETTE },
            });
        }

        try {
            const userAgents = Array(1)
                .fill(0)
                .map(() => new UserAgent().toString());

            let result: { data: { restaurant: { aggregateRatings: { thefork: { reviewCount: number; ratingValue: number } } } } };
            for (const userAgent of userAgents) {
                try {
                    const config: AxiosRequestConfig = {
                        method: 'POST',
                        url: 'https://www.thefork.fr/api/graphql',
                        headers: {
                            'User-Agent': userAgent,
                            'Accept-Language': 'fr-FR',
                            Accept: '*/*',
                            'Content-Type': 'application/json',
                            Host: 'www.thefork.fr',
                            Origin: 'https://www.thefork.fr',
                            Connection: 'keep-alive',
                            Cookie: 'datadome=fnfSlkPFmlvlIPaKNmr4iFFmsKghWYKdIs60MxA3eZESDHFkW_fWJtKAvtUN~PWlO8ls18jIvPTMSv_ucLIqQAD7MeofsW3ZdbBfi2i9SSCEiObpvbIytb1f0xfcmmC7',
                        },
                        data: {
                            operationName: 'getRPTrackingInfos',
                            variables: {
                                restaurantId: platform?.socialId,
                            },
                            query: 'query getRPTrackingInfos($restaurantId: ID!) { restaurant(restaurantId: $restaurantId) { aggregateRatings { thefork { reviewCount ratingValue  }  } }}',
                        },
                        proxy: this._proxy,
                    };

                    result = await axios.request(config).then((res) => res.data);
                    logger.info('[SUCCESS_FETCHING_LAFOURCHETTE_RATING]', { result });
                    platformRatingsFetchCounter.add(1, {
                        source: PlatformKey.LAFOURCHETTE,
                        status: 'success',
                    });

                    return result?.data?.restaurant?.aggregateRatings?.thefork
                        ? (result.data.restaurant.aggregateRatings.thefork.ratingValue ?? null)
                        : undefined;
                } catch (e: any) {
                    if (e.response?.status === 404 && e?.response?.data?.errors?.[0]?.code === 'RESTAURANT_NOT_FOUND') {
                        platformRatingsFetchCounter.add(1, {
                            source: PlatformKey.LAFOURCHETTE,
                            status: 'success',
                        }); // Consider success even if restaurant is not found
                        logger.info('[RESTAURANT_CHURNED_FROM_THE_FORK]', { message: 'Restaurant not found', platform });
                        await this._platformsRepository.findOneAndUpdate({
                            filter: { _id: platform._id },
                            update: { foundStatusOnPlatform: FoundStatusOnPlatform.NOT_FOUND },
                        });
                        return;
                    }
                    logger.warn('[ERROR_FETCHING_LAFOURCHETTE_RATING_WITH_USER_AGENT]', { userAgent, error: e.message });
                }
            }
            logger.error('[ERROR_FETCHING_LAFOURCHETTE_RATING]', { platform });
            platformRatingsFetchCounter.add(1, {
                source: PlatformKey.LAFOURCHETTE,
                status: 'failure',
            });
            return;
        } catch (e) {
            logger.error('[LAFOURCHETTE_RATING_UNEXPECTED_ERROR]', { e, platform });
            throw e;
        }
    };

    async insertTodayFollowers() {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'Method not implemented',
            metadata: {
                platform: PlatformKey.LAFOURCHETTE,
            },
        });
    }
}
