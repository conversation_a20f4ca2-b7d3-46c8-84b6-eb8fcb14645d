import { StoryToDuplicateDto } from '@malou-io/package-dto';
import { PlatformKey, PostPublicationStatus, RecurrentStoryFrequency, RemoveMethodsFromEntity } from '@malou-io/package-utils';

import { SocialPostMedia } from ':modules/posts-v2/social-posts/models/social-post-media';

type IStoryToDuplicate = RemoveMethodsFromEntity<StoryToDuplicate> & { id: string };

/** This is basically the response of GET /posts/to-duplicate */
export class StoryToDuplicate implements IStoryToDuplicate {
    id: string;
    bindingId: string;
    platformKeys: PlatformKey[];
    published: PostPublicationStatus;
    plannedPublicationDate: Date | null;
    socialCreatedAt: Date | null;
    medias: SocialPostMedia[];
    recurrentStoryFrequency?: RecurrentStoryFrequency;

    constructor(data: IStoryToDuplicate) {
        this.id = data.id;
        this.bindingId = data.bindingId;
        this.platformKeys = data.platformKeys;
        this.published = data.published;
        this.plannedPublicationDate = data.plannedPublicationDate;
        this.medias = data.medias;
        this.socialCreatedAt = data.socialCreatedAt;
        this.recurrentStoryFrequency = data.recurrentStoryFrequency;
    }

    static fromDto(dto: StoryToDuplicateDto): StoryToDuplicate {
        return new StoryToDuplicate({
            id: dto.id,
            bindingId: dto.bindingId,
            platformKeys: dto.platformKeys,
            published: dto.published,
            plannedPublicationDate: dto.plannedPublicationDate ? new Date(dto.plannedPublicationDate) : null,
            medias: dto.medias.map((media) => SocialPostMedia.fromDto(media)),
            socialCreatedAt: dto.socialCreatedAt ? new Date(dto.socialCreatedAt) : null,
            recurrentStoryFrequency: dto.recurrentStoryFrequency,
        });
    }
}
