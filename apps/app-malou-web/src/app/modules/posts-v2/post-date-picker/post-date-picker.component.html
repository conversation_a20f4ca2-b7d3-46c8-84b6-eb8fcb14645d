@if (recurrentStoryFrequency() && recurrentStoryFrequency() !== RecurrentStoryFrequency.NONE && size() === PostDatePickerSize.SMALL) {
    <div
        class="malou-text-10--medium flex h-6 items-center rounded-[3px] bg-malou-color-background-pending px-2 text-malou-color-text-purple--light"
        [ngClass]="{
            'cursor-pointer': !disabled(),
            'cursor-default': disabled(),
        }"
        [id]="heapTrackBtnId()"
        (click)="!disabled() && openCalendar()">
        {{
            'stories.recurrent_story_frequency'
                | translate
                    : {
                          frequency:
                              recurrentStoryFrequency() ?? RecurrentStoryFrequency.NONE
                              | enumTranslate: 'recurrent_story_frequency' : undefined : displayDayOfSelectedDate(),
                          time: selectedDate() | date: 'shortTime',
                      }
        }}
    </div>
} @else {
    <div
        class="flex items-center gap-x-2 bg-malou-color-background-light text-malou-color-text-1"
        [ngClass]="{
            'cursor-pointer': !disabled(),
            'cursor-default': disabled(),
            'border border-malou-color-border-primary': withBorder(),
            'h-6 rounded-[3px] px-2': size() === PostDatePickerSize.SMALL,
            'h-[50px] rounded-[5px] px-3': size() === PostDatePickerSize.BIG,
        }"
        [id]="heapTrackBtnId()"
        (click)="!disabled() && openCalendar()">
        <mat-icon class="!h-4 !w-4" color="primary" [svgIcon]="SvgIcon.CALENDAR"></mat-icon>
        @if (!recurrentStoryFrequency() || recurrentStoryFrequency() === RecurrentStoryFrequency.NONE) {
            <div
                class="mt-[1px] first-letter:uppercase"
                [ngClass]="{
                    'malou-text-10--regular': size() === PostDatePickerSize.SMALL,
                    'malou-text-12--semibold': size() === PostDatePickerSize.BIG,
                }">
                @if (selectedDate(); as selectedDate) {
                    {{ selectedDate | date: 'fullDate' }}, {{ selectedDate | date: 'shortTime' }}
                } @else {
                    --
                }
            </div>
        } @else {
            <div class="mt-[1px]">
                <span class="malou-text-12--semibold">
                    {{
                        recurrentStoryFrequency() ?? RecurrentStoryFrequency.NONE
                            | enumTranslate: 'recurrent_story_frequency_short' : undefined : displayDayOfSelectedDate()
                    }}
                    @if (selectedDate(); as selectedDate) {
                        {{ 'common.at' | translate }} {{ selectedDate | date: 'shortTime' }}
                    }
                </span>
                <span class="malou-text-12--regular">
                    ({{
                        'post_date_picker.recurring_post.starting_date'
                            | translate: { date: selectedDate() ? (selectedDate() | date: 'fullDate') : '--' }
                    }})
                </span>
            </div>
        }
    </div>
}
