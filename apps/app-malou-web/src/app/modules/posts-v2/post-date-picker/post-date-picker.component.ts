import { DatePipe, NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, model } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

import { ApplicationLanguage, DAYS, RecurrentStoryFrequency } from '@malou-io/package-utils';

import { LocalStorage } from ':core/storage/local-storage';
import { PostDatePickerModalComponent } from ':modules/posts-v2/post-date-picker/post-date-picker-modal/post-date-picker-modal.component';
import {
    PostDatePickerModalProps,
    PostDatePickerModalResult,
} from ':modules/posts-v2/post-date-picker/post-date-picker-modal/post-date-picker-modal.interface';
import { PostDatePickerSize } from ':modules/posts-v2/post-date-picker/post-date-picker.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-post-date-picker',
    templateUrl: './post-date-picker.component.html',
    styleUrls: ['./post-date-picker.component.scss'],
    imports: [NgClass, MatIconModule, TranslateModule, DatePipe, EnumTranslatePipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [EnumTranslatePipe],
})
export class PostDatePickerComponent {
    readonly selectedDate = model<Date | null>(null);
    readonly disabled = input<boolean>(false);
    readonly size = input<PostDatePickerSize>(PostDatePickerSize.SMALL);
    readonly withBorder = input<boolean>(false);
    readonly heapTrackBtnId = input<string>();
    readonly recurrentStoryFrequency = model<RecurrentStoryFrequency>();
    readonly isStory = input<boolean>(false);

    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _enumTranslatePipe = inject(EnumTranslatePipe);

    readonly SvgIcon = SvgIcon;
    readonly PostDatePickerSize = PostDatePickerSize;
    readonly RecurrentStoryFrequency = RecurrentStoryFrequency;

    readonly displayDayOfSelectedDate = computed((): Record<string, string> => {
        const postDate = this.selectedDate();
        if (!postDate) {
            return {};
        }
        const day = postDate.getDay();
        const displayedDay = this._enumTranslatePipe.transform(DAYS[Math.abs(day - 1)], 'days');
        return { day: this._lang === ApplicationLanguage.EN ? displayedDay : displayedDay.toLowerCase() };
    });

    private readonly _lang = LocalStorage.getLang();

    openCalendar(): void {
        this._customDialogService
            .open<PostDatePickerModalComponent, PostDatePickerModalProps, PostDatePickerModalResult>(PostDatePickerModalComponent, {
                data: {
                    selectedDate: this.selectedDate(),
                    isStory: this.isStory(),
                    recurrentStoryFrequency: this.recurrentStoryFrequency(),
                },
                disableClose: false,
                maxHeight: '610px',
                minWidth: this.isStory() ? '750px' : undefined,
            })
            .afterClosed()
            .subscribe((result) => {
                if (result?.selectedDate) {
                    this.selectedDate.set(result.selectedDate);
                    this.recurrentStoryFrequency.set(result.recurrentStoryFrequency);
                }
            });
    }
}
