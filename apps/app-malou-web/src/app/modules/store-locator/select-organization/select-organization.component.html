@if (organizations().length > 1) {
    <div
        class="malou-card !mx-[10px] !my-0 !mt-[35px] max-h-fit border border-malou-color-background-dark bg-malou-color-background-dark p-4">
        <div class="flex items-center justify-between">
            <div class="malou-text-15--semibold malou-color-text-1">
                <span class="text-[25px]">👑</span>
                {{ 'store_locator.select_organization.title' | translate }}
            </div>
            <app-select
                class="w-[400px]"
                [values]="organizations()"
                [formControl]="organizationControl"
                [theme]="SelectBaseDisplayStyle.WITH_BACKGROUND"
                [displayWith]="organizationDisplayWith"
                (selectChange)="onOrganizationChange($event)">
            </app-select>
        </div>
    </div>
}
