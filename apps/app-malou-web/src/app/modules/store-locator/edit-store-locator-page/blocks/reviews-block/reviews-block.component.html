<div
    class="relative flex h-fit sm:flex-row"
    [ngClass]="{
        'cursor-not-allowed': shouldShowCursorNotAllowed(),
        'cursor-pointer opacity-60 hover:opacity-100': !isBlockSelected(),
    }"
    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.REVIEWS_WRAPPER]"
    (click)="handleShowFormBlock(StoreLocatorPageBlockType.REVIEWS)">
    <div class="mx-auto flex w-full flex-col items-center justify-center px-6 py-14">
        <h2
            class="max-w-[95%] break-words pb-8 text-center text-3xl font-extrabold uppercase"
            [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.REVIEWS_TITLE]">
            {{ title() }}
        </h2>

        <div class="no-scrollbar mx-auto flex w-full flex-wrap justify-center">
            @for (review of reviews(); track $index) {
                <div class="m-2 min-h-[270px] w-[320px] flex-shrink-0 rounded-xl bg-white px-7 py-3 pb-7">
                    <div class="flex w-full items-center justify-between">
                        @if (getProfilePictureUrl(review)) {
                            <img
                                class="h-[35px] w-[35px] rounded-full object-cover"
                                [alt]="review.userName"
                                [src]="getProfilePictureUrl(review)" />
                        } @else {
                            <div
                                class="flex h-[35px] w-[35px] items-center justify-center rounded-full text-sm text-white"
                                [ngStyle]="{ 'background-color': getProfileAvatar(review)?.color }">
                                {{ getProfileAvatar(review)?.initials || (review.userName ? review.userName[0] : '') }}
                            </div>
                        }

                        <div class="ml-2 mr-2 flex min-w-0 flex-1 flex-col items-start overflow-hidden">
                            <p class="w-full truncate text-xs font-semibold text-malou-color-text-1">{{ review.userName }}</p>
                            @if (review.publishedAt) {
                                <p class="text-xxs text-gray-500">{{ review.publishedAt }}</p>
                            }
                        </div>
                        <div class="flex flex-shrink-0 rounded-md">
                            <img
                                class="!h-8 !w-8 rounded-md"
                                alt="Google"
                                style="object-fit: contain"
                                [src]="PlatformKey.GMB | platformLogoPathResolver" />
                        </div>
                    </div>
                    <div class="my-4 flex">
                        @for (star of [].constructor(review.starsCount); track $index) {
                            <mat-icon class="mx-1 text-yellow-500" [svgIcon]="SvgIcon.STAR"></mat-icon>
                        }
                    </div>
                    <p
                        class="line-clamp-8 overflow-hidden text-xs text-gray-700"
                        style="display: -webkit-box; -webkit-box-orient: vertical; line-clamp: 8; -webkit-line-clamp: 8">
                        {{ review.content }}
                    </p>
                </div>
            }
        </div>

        @if (cta()) {
            <div class="mt-8 flex justify-center">
                <a
                    class="my-3 block w-[320px] border p-4 text-center text-sm font-extralight uppercase"
                    target="_blank"
                    [attr.aria-label]="cta()?.text"
                    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.REVIEWS_CTA]"
                    [href]="cta()?.url">
                    {{ cta()?.text }}
                </a>
            </div>
        }
    </div>
    <div
        class="absolute left-0 top-0 h-full w-full bg-transparent hover:border-4 hover:border-malou-color-primary"
        [class.border-malou-color-primary]="isBlockSelected()"
        [class.border-4]="isBlockSelected()"></div>
</div>
