<div
    class="relative flex h-fit min-h-fit sm:flex-row"
    [ngClass]="{
        'cursor-not-allowed': shouldShowCursorNotAllowed(),
        'cursor-pointer opacity-60 hover:opacity-100': !isBlockSelected(),
    }"
    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]"
    (click)="handleShowFormBlock(StoreLocatorPageBlockType.INFORMATION)">
    <div class="h-[720px] w-[47%] overflow-hidden">
        <img
            class="h-full w-full object-cover"
            [alt]="image().alt"
            [defaultImage]="ImageAssets.DEFAULT_POST | imagePathResolver"
            [lazyLoad]="image().src || ''" />
    </div>

    <div class="flex w-[50%] flex-1 flex-col gap-3 px-16 py-8">
        @if (isNotOpenedYet()) {
            <p
                class="mb-4 p-6 text-center text-xl font-bold lg:text-3xl"
                [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_OPENING_SOON_BANNER]">
                {{ informationBlockTranslation().opening_soon }}
            </p>
        }

        <h1 class="break-words text-5xl uppercase" [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]">
            {{ title() }}
        </h1>

        <div class="mt-6 grid auto-cols-auto grid-cols-[repeat(auto-fit,minmax(50%,1fr))] gap-x-3 gap-y-2 font-normal">
            <div class="mb-0 sm:mb-4">
                <a target="_blank" [href]="itineraryUrl()">
                    <p class="flex items-start gap-6 text-[16px] sm:gap-4">
                        <mat-icon
                            class="!h-5 !w-5"
                            [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]"
                            [svgIcon]="SvgIcon.PIN"></mat-icon>
                        <span>{{ fullAddress() }}</span>
                    </p>
                </a>
            </div>

            <div class="mb-0">
                @if (phoneNumber()) {
                    <a [href]="'tel:' + phoneNumber()">
                        <p class="flex items-center gap-4 text-[16px]">
                            <mat-icon
                                class="!h-5 !w-5"
                                [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]"
                                [svgIcon]="SvgIcon.PHONE_FILLED"></mat-icon>

                            {{ phoneNumber() }}
                        </p>
                    </a>
                }
            </div>

            @if (informationFormattedHours().length > 0) {
                <div class="col-span-2 mb-0">
                    <div class="my-4 w-full border-t" [ngStyle]="{ borderColor: primaryColor() }"></div>
                </div>

                <div class="row-start-3">
                    <div class="flex gap-4">
                        <mat-icon
                            class="!h-5 !w-5"
                            [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]"
                            [svgIcon]="SvgIcon.HOUR"></mat-icon>

                        <div class="flex flex-col gap-2">
                            @for (day of informationFormattedHours().slice(0, 4); track day) {
                                <p class="text-[16px]" [ngStyle]="day.extraStyle">
                                    {{ day.value }}
                                </p>
                            }
                        </div>
                    </div>
                </div>

                <div class="row-start-3 mb-4 mt-0">
                    <div class="flex flex-col gap-2">
                        @for (day of informationFormattedHours().slice(4, 8); track day) {
                            <p class="text-[16px]" [ngStyle]="day.extraStyle">
                                {{ day.value }}
                            </p>
                        }
                    </div>
                </div>
            }

            @if (informationAttributes()) {
                <div class="col-span-2 mb-0">
                    <div class="my-4 w-full border-t" [ngStyle]="{ borderColor: primaryColor() }"></div>
                </div>

                <div class="col-span-2 mb-0">
                    <div class="flex items-center gap-4 text-[16px]">
                        <mat-icon
                            class="!h-7 !w-7"
                            [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]"
                            [svgIcon]="SvgIcon.CART"></mat-icon>

                        <p class="text-[16px]">
                            {{ informationAttributes() }}
                        </p>
                    </div>
                </div>
            }

            @if (informationPaymentMethods()) {
                <div class="col-span-2 mb-0">
                    <div class="my-4 w-full border-t" [ngStyle]="{ borderColor: primaryColor() }"></div>
                </div>

                <div class="col-span-2 row-start-7">
                    <div class="flex items-center gap-4">
                        <mat-icon
                            class="!h-5 !w-5"
                            [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]"
                            [svgIcon]="SvgIcon.VOUCHER"></mat-icon>

                        <p class="text-[16px]">
                            {{ informationPaymentMethods() }}
                        </p>
                    </div>
                </div>
            }
        </div>
        <div class="mt-14 flex w-full justify-center gap-4 text-sm">
            @if (primaryInformationCta()) {
                <a
                    class="w-fit items-center border-[1px] border-solid px-7 py-4 font-extralight shadow-md"
                    target="_blank"
                    [href]="primaryInformationCta()?.url"
                    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1]">
                    <span class="uppercase">{{ primaryInformationCta()?.text }}</span>
                </a>
            }

            @if (secondaryInformationCta()) {
                <a
                    class="w-fit items-center border-[1px] border-solid px-7 py-4 font-extralight shadow-md"
                    target="_blank"
                    [href]="secondaryInformationCta()?.url"
                    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2]">
                    <span class="uppercase">{{ secondaryInformationCta()?.text }}</span>
                </a>
            }
        </div>
    </div>

    <div
        class="absolute left-0 top-0 h-full w-full bg-transparent hover:border-4 hover:border-malou-color-primary"
        [class.border-malou-color-primary]="isBlockSelected()"
        [class.border-4]="isBlockSelected()"></div>
</div>
