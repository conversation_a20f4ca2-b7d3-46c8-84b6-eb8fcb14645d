<div
    class="edit-modal-container"
    [ngClass]="{
        'h-[90vh]': !displayCloseModal() || (displayCloseModal() && isPhoneScreen()),
        'h-[350px]': displayCloseModal() && !isPhoneScreen(),
    }">
    <ng-container [ngTemplateOutlet]="displayCloseModal() ? closeModal : editModal"></ng-container>
</div>

<ng-template #closeModal>
    <app-close-without-saving-modal (onConfirm)="confirmClose()" (onCancel)="displayCloseModal.set(false)">
    </app-close-without-saving-modal>
</ng-template>

<ng-template #editModal>
    <div class="malou-dialog">
        <div class="malou-dialog__header">
            <div class="flex items-center gap-2">
                <mat-icon class="!h-9 !w-9 text-malou-color-chart-purple--accent" [svgIcon]="SvgIcon.MAGIC_WAND"></mat-icon>
                <span class="malou-text-24--bold">{{ 'store_locator.edit_languages_modal.title' | translate }}</span>
            </div>

            <button class="malou-btn-icon" mat-icon-button (click)="close()">
                <mat-icon color="primary" [svgIcon]="SvgIcon.CROSS"></mat-icon>
            </button>
        </div>

        <div class="malou-dialog__body">
            <ng-container [ngTemplateOutlet]="formTemplate"></ng-container>
        </div>

        <div class="malou-dialog__footer md:justify-between">
            <button class="malou-btn-raised--secondary !h-11 md:grow" mat-raised-button (click)="close()">
                {{ 'common.cancel' | translate }}
            </button>

            <button class="malou-btn-raised--primary !h-11 md:grow" mat-raised-button (click)="save()">
                {{ 'common.apply' | translate }}
            </button>
        </div>
    </div>
</ng-template>

<ng-template #formTemplate>
    <div class="flex flex-col gap-4" [formGroup]="languagesForm">
        <div class="malou-text-14--bold text-malou-color-text-1">
            {{ 'store_locator.edit_languages_modal.primary_language.section_title' | translate }}
        </div>

        <div class="flex">
            <app-select-languages
                class="w-full"
                [formControl]="primaryLanguage"
                [title]="'store_locator.edit_languages_modal.primary_language.language_label' | translate"
                [values]="allLanguages()"
                [multiSelection]="false"
                (selectLanguagesChange)="onPrimaryLanguageChange($event)">
            </app-select-languages>
        </div>

        <div class="malou-text-14--bold mt-4 text-malou-color-text-1">
            {{ 'store_locator.edit_languages_modal.secondary_languages.section_title' | translate }}
        </div>

        @for (secondaryLanguage of secondaryLanguages.controls; track $index; let index = $index; let last = $last) {
            <div class="flex flex-col gap-4">
                <div class="group flex items-center gap-2">
                    <app-select-languages
                        class="w-full"
                        [formControl]="secondaryLanguage"
                        [title]="'store_locator.edit_languages_modal.secondary_languages.language_label' | translate: { index: index + 1 }"
                        [placeholder]="'store_locator.edit_languages_modal.secondary_languages.choose_language_placeholder' | translate"
                        [values]="availableLanguages()"
                        [multiSelection]="false"
                        (selectLanguagesChange)="onSecondaryLanguageChange(index, $event)">
                    </app-select-languages>

                    <div class="invisible mt-6 group-hover:visible md:visible">
                        <mat-icon
                            class="malou-color-chart-pink--accent !h-5 !w-5 cursor-pointer"
                            svgIcon="trash"
                            (click)="removeSecondaryLanguage(index)">
                        </mat-icon>
                    </div>
                </div>

                @if (last && availableLanguages().length !== 0) {
                    <div class="flex">
                        <button
                            class="malou-btn-flat"
                            mat-button
                            [disabled]="!availableLanguages().length"
                            (click)="addSecondaryLanguage()">
                            <mat-icon svgIcon="add"></mat-icon>
                            {{ 'store_locator.edit_languages_modal.secondary_languages.add_language_button' | translate }}
                        </button>
                    </div>
                }
            </div>
        }
    </div>
</ng-template>
