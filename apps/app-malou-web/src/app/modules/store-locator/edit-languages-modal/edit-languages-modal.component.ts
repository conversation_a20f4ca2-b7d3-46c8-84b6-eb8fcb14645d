import { Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal, WritableSignal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { ApplicationLanguage, isNotNil, StoreLocatorLanguage } from '@malou-io/package-utils';

import { ScreenSizeService } from ':core/services/screen-size.service';
import { EditLanguagesModalInputData } from ':modules/store-locator/edit-languages-modal/edit-languages-modal.interface';
import { StoreLocatorService } from ':modules/store-locator/store-locator.service';
import { CloseWithoutSavingModalComponent } from ':shared/components/close-without-saving-modal/close-without-saving-modal.component';
import { SelectLanguagesComponent } from ':shared/components/select-languages/select-languages.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

// Helper function to get valid ApplicationLanguages that exist in StoreLocatorLanguage
const getValidApplicationLanguages = (): ApplicationLanguage[] =>
    Object.values(ApplicationLanguage).filter((lang) =>
        Object.values(StoreLocatorLanguage).includes(lang as unknown as StoreLocatorLanguage)
    );

@Component({
    selector: 'app-edit-languages-modal',
    imports: [
        NgClass,
        NgTemplateOutlet,
        CloseWithoutSavingModalComponent,
        TranslateModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        SelectLanguagesComponent,
        MatButtonModule,
    ],
    templateUrl: './edit-languages-modal.component.html',
    styleUrl: './edit-languages-modal.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditLanguagesModalComponent implements OnInit {
    private readonly _dialogRef = inject(MatDialogRef<EditLanguagesModalComponent>);
    private readonly _screenSizeService = inject(ScreenSizeService);
    private readonly _storeLocatorService = inject(StoreLocatorService);
    private readonly _translate = inject(TranslateService);
    readonly data: EditLanguagesModalInputData = inject(MAT_DIALOG_DATA);

    private _initialFormValue: { primary: string; secondary: string[] } | null = null;

    readonly SvgIcon = SvgIcon;

    readonly languagesForm = new FormGroup({
        primary: new FormControl<string>(this.data.languages.primary, {
            nonNullable: true,
            validators: [Validators.required],
        }),
        secondary: new FormArray<FormControl<string | null>>([]),
    });
    readonly displayCloseModal = signal(false);
    readonly isPhoneScreen = toSignal(this._screenSizeService.isPhoneScreen$, { initialValue: this._screenSizeService.isPhoneScreen });
    readonly allLanguages: WritableSignal<ApplicationLanguage[]> = signal(getValidApplicationLanguages());
    readonly availableLanguages: WritableSignal<ApplicationLanguage[]> = signal([]);

    get primaryLanguage(): FormControl<string> {
        return this.languagesForm.controls.primary;
    }

    get secondaryLanguages(): FormArray<FormControl<string | null>> {
        return this.languagesForm.controls.secondary;
    }

    ngOnInit(): void {
        this._initializeForm();
    }

    addSecondaryLanguage(): void {
        const secondaryLanguagesArray = this.languagesForm.controls.secondary;
        const isOneLanguageAvailable = this.availableLanguages().length === 1;
        const control = new FormControl<string | null>(isOneLanguageAvailable ? this.availableLanguages()[0] : null, {
            nonNullable: true,
        });
        secondaryLanguagesArray.push(control);
        if (isOneLanguageAvailable) {
            this._updateAvailableLanguages();
        }
    }

    removeSecondaryLanguage(index: number): void {
        const secondaryLanguagesArray = this.languagesForm.controls.secondary;
        if (secondaryLanguagesArray.length > 1) {
            secondaryLanguagesArray.removeAt(index);
        } else {
            secondaryLanguagesArray.at(index).setValue(null);
        }
        this._updateAvailableLanguages();
    }

    save(): void {
        const { primary, secondary } = this.languagesForm.getRawValue();
        const languagesUpdate = {
            primary: primary as StoreLocatorLanguage,
            secondary: secondary.filter(isNotNil) as StoreLocatorLanguage[],
        };
        const organizationId = this.data.organizationId;
        this._storeLocatorService.updateOrganizationConfigLanguages(organizationId, languagesUpdate).subscribe({
            next: (updatedConfig) => {
                this._dialogRef.close(updatedConfig);
            },
            error: (error) => {
                console.error('Error updating languages settings:', error);
            },
        });
    }

    confirmClose(): void {
        this._dialogRef.close();
    }

    close(): void {
        if (this._hasFormChanged()) {
            this.displayCloseModal.set(true);
        } else {
            this.confirmClose();
        }
    }

    onPrimaryLanguageChange(lang: ApplicationLanguage[] | ApplicationLanguage): void {
        if (Array.isArray(lang)) {
            lang = lang[0];
        }

        this.primaryLanguage.setValue(lang);

        // Remove from secondary if it exists
        const secondaryIndex = this.secondaryLanguages.value.indexOf(lang);
        if (secondaryIndex !== -1) {
            this.removeSecondaryLanguage(secondaryIndex);
        }

        this._updateAvailableLanguages();
    }

    onSecondaryLanguageChange(index: number, lang: ApplicationLanguage[] | ApplicationLanguage): void {
        if (Array.isArray(lang)) {
            lang = lang[0];
        }
        this.secondaryLanguages.at(index).setValue(lang);
        this._updateAvailableLanguages();
    }

    private _hasFormChanged(): boolean {
        const formSnapshot = this._getFormSnapshot();
        return JSON.stringify(formSnapshot) !== JSON.stringify(this._initialFormValue);
    }

    private _initializeForm(): void {
        const { primary, secondary } = this.data.languages;

        const primaryLanguage = primary !== StoreLocatorLanguage.UNDETERMINED ? primary : this._getDefaultPrimaryLanguage();

        this.languagesForm.patchValue({ primary: primaryLanguage });

        const secondaryLanguagesArray = this.languagesForm.controls.secondary;
        secondaryLanguagesArray.clear();

        if (secondary.length > 0) {
            secondary.forEach((language) => {
                const control = new FormControl<string>(language, { nonNullable: true });
                secondaryLanguagesArray.push(control);
            });
        } else {
            // If no secondary languages are provided, add an empty control
            const control = new FormControl<string | null>(null, { nonNullable: true });
            secondaryLanguagesArray.push(control);
        }

        this._updateAvailableLanguages();
        this._initialFormValue = this._getFormSnapshot();
    }

    private _getFormSnapshot(): { primary: string; secondary: string[] } {
        return {
            primary: this.primaryLanguage.value as string,
            secondary: this.secondaryLanguages.value.filter(isNotNil) as string[],
        };
    }

    private _updateAvailableLanguages(): void {
        const primaryValue = this.primaryLanguage.value;
        const secondaryValues = this.secondaryLanguages.value.filter(isNotNil);

        this.availableLanguages.set(this.allLanguages().filter((lang) => lang !== primaryValue && !secondaryValues.includes(lang)));
    }

    private _getDefaultPrimaryLanguage(): StoreLocatorLanguage {
        const defaultLang = this._translate.getDefaultLang();
        if (defaultLang && Object.values(StoreLocatorLanguage).includes(defaultLang as StoreLocatorLanguage)) {
            return defaultLang as StoreLocatorLanguage;
        }
        return StoreLocatorLanguage.FR;
    }
}
