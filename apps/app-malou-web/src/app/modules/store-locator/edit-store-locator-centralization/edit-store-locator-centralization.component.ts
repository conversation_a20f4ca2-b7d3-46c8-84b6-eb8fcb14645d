import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    computed,
    ElementRef,
    HostListener,
    inject,
    OnInit,
    signal,
    ViewChild,
    WritableSignal,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';

import { StoreLocatorCentralizationPageMapBlockFormComponent } from ':modules/store-locator/edit-store-locator-centralization/blocks/map/form/map-block-form.component';
import { StoreLocatorCentralizationPageMapBlockComponent } from ':modules/store-locator/edit-store-locator-centralization/blocks/map/map-block.component';
import { EditStoreLocatorCentralizationContext } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.context';
import {
    EditStoreLocatorCentralizationModalInputData,
    StoreLocatorCentralizationBlockType,
} from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.interface';
import { StoreLocatorPageFooterBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/footer-block/footer-block.component';
import { StoreLocatorPageHeaderBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/header-block/header-block.component';
import { StoreLocatorOrganizationStylesConfiguration } from ':modules/store-locator/shared/edit-store-locator/models/store-locator-organization-styles-configuration';
import { getFontFamily, loadDynamicFont } from ':modules/store-locator/shared/edit-store-locator/utils/inject-font-family';
import { scalePreview } from ':modules/store-locator/shared/edit-store-locator/utils/preview-scale';
import { ButtonComponent } from ':shared/components/button/button.component';
import { MenuButtonSize } from ':shared/components/menu-button-v3/menu-button-v3.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-store-locator-edit-centralization',
    templateUrl: './edit-store-locator-centralization.component.html',
    styleUrls: ['./edit-store-locator-centralization.component.scss'],
    imports: [
        MatButtonModule,
        MatProgressSpinnerModule,
        NgTemplateOutlet,
        MatIconModule,
        NgStyle,
        TranslateModule,
        NgClass,
        ButtonComponent,
        StoreLocatorCentralizationPageMapBlockFormComponent,
        StoreLocatorCentralizationPageMapBlockComponent,
        StoreLocatorPageHeaderBlockComponent,
        StoreLocatorPageFooterBlockComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditStoreLocatorCentralizationModalComponent implements OnInit, AfterViewInit {
    private readonly _editStoreLocatorCentralizationContext = inject(EditStoreLocatorCentralizationContext);
    private readonly _dialogRef = inject(MatDialogRef<EditStoreLocatorCentralizationModalComponent>);

    readonly data: EditStoreLocatorCentralizationModalInputData = inject(MAT_DIALOG_DATA);

    @ViewChild('sitePreview') sitePreviewRef!: ElementRef<HTMLDivElement>;

    readonly MenuButtonSize = MenuButtonSize;
    readonly SvgIcon = SvgIcon;
    readonly StoreLocatorCentralizationBlockType = StoreLocatorCentralizationBlockType;

    readonly isLoading: WritableSignal<boolean> = signal(true);

    readonly textFontFamilyClass = computed(() => getFontFamily('primary'));

    readonly isBlockInError = computed(() => this._editStoreLocatorCentralizationContext.isBlockInError().isError);

    readonly shouldDisableModal = computed(() => this._editStoreLocatorCentralizationContext.shouldDisableModal());

    readonly shouldAllowToSaveAsDraftOrPublish = computed(() => {
        const centralizationPageState = this._editStoreLocatorCentralizationContext.storeCentralizationPageState();
        const organizationStyleConfiguration = this._editStoreLocatorCentralizationContext.organizationStyleConfiguration();

        const isCentralizationPageDirty = centralizationPageState?.isDirty() ?? false;
        const isOrganizationStyleConfigurationDirty = organizationStyleConfiguration?.isDirty() ?? false;

        return isCentralizationPageDirty || isOrganizationStyleConfigurationDirty;
    });

    readonly selectedBlock: WritableSignal<StoreLocatorCentralizationBlockType> = signal(StoreLocatorCentralizationBlockType.MAP);

    ngOnInit(): void {
        this.isLoading.set(true);
        this._editStoreLocatorCentralizationContext.dialogRef.set(this._dialogRef);
        this._editStoreLocatorCentralizationContext.organizationStyleConfiguration.set(
            new StoreLocatorOrganizationStylesConfiguration(this.data.organizationConfiguration.styles)
        );
        loadDynamicFont(this.data.organizationConfiguration.styles.fonts);
        this.isLoading.set(false);
    }

    ngAfterViewInit(): void {
        scalePreview(this.sitePreviewRef);
    }

    @HostListener('window:resize')
    onResize() {
        scalePreview(this.sitePreviewRef);
    }

    closeEditModal(): void {
        this._dialogRef.close();
    }

    onSaveAsDraft(): void {
        this._editStoreLocatorCentralizationContext.saveAsDraft();
    }

    onPublish(): void {
        console.info('Publish clicked');
    }
}
