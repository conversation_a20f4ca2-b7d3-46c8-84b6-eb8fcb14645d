import z from 'zod';

import {
    DeviceType,
    PlatformKey,
    PostPublicationStatus,
    PublicationErrorCode,
    RecurrentStoryFrequency,
    StoriesListFilter,
} from '@malou-io/package-utils';

import { restaurantIdParamsTransformValidator } from '../common';
import { PostAuthorDto, SocialPostMediaDto } from '../posts-v2/get-social-posts.dto';

export interface StoryItemDto {
    id: string;
    published: PostPublicationStatus;
    isPublishing: boolean;
    bindingId?: string;
    plannedPublicationDate: string | null;
    platformKeys: PlatformKey[];
    medias: SocialPostMediaDto[];
    feedbackMessageCount: number;
    author?: PostAuthorDto;
    socialCreatedAt?: string;
    sortDate?: string;
    mostRecentPublicationErrorCode?: PublicationErrorCode;
    socialLink?: string;
    createdFromDeviceType?: DeviceType;
    recurrentStoryFrequency: RecurrentStoryFrequency;
}

export const getStoriesQueryValidator = z
    .object({
        cursor: z.string().datetime().nullish(),
        limit: z.coerce.number().int().positive().nullish(),
        filter: z.nativeEnum(StoriesListFilter).nullish(),
    })
    .transform((data) => ({
        cursor: data.cursor ? new Date(data.cursor) : null,
        limit: data.limit ?? null,
        filter: data.filter ?? StoriesListFilter.ALL,
    }));

export type GetStoriesQueryDto = z.infer<typeof getStoriesQueryValidator>;

export const getStoriesParamsValidator = restaurantIdParamsTransformValidator;
export type GetStoriesParamsDto = z.infer<typeof getStoriesParamsValidator>;
