import { z } from 'zod';

import { PlatformKey, PostPublicationStatus, RecurrentStoryFrequency } from '@malou-io/package-utils';

import { SocialPostMediaDto } from '../posts-v2/get-social-posts.dto';
import { objectIdValidator } from '../utils';

export const getStoriesToDuplicateQueryValidator = z
    .object({
        story_ids: z.array(objectIdValidator).default([]),
        story_binding_ids: z.array(objectIdValidator).default([]),
    })
    .transform((data) => ({ storyIds: data.story_ids, storyBindingIds: data.story_binding_ids }));

export type GetStoriesToDuplicateQueryDto = z.infer<typeof getStoriesToDuplicateQueryValidator>;

export type StoryToDuplicateDto = {
    id: string;
    bindingId: string;
    platformKeys: PlatformKey[];
    published: PostPublicationStatus;
    plannedPublicationDate: string | null;
    medias: SocialPostMediaDto[];
    socialCreatedAt: string | null;
    recurrentStoryFrequency?: RecurrentStoryFrequency;
};
