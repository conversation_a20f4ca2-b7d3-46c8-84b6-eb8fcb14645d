import { z } from 'zod';

import { PlatformKey, PostPublicationStatus, RecurrentStoryFrequency } from '@malou-io/package-utils';

import { postMediaValidator, userTagsListValidator } from '../posts-v2';
import { objectIdValidator } from '../utils/validators';

export const updateStoryBodyValidator = z
    .object({
        id: objectIdValidator,
        platformKeys: z.array(z.nativeEnum(PlatformKey)),
        published: z.nativeEnum(PostPublicationStatus),
        isPublishing: z.boolean().optional(),
        feedbackId: objectIdValidator.nullish(),
        plannedPublicationDate: z.string().datetime(),
        medias: z.array(postMediaValidator),
        userTagsList: userTagsListValidator,
        author: z
            .object({
                id: objectIdValidator,
                name: z.string().optional(),
                lastname: z.string().optional(),
                picture: z.string().nullish(),
            })
            .nullish(),
        recurrentStoryFrequency: z.nativeEnum(RecurrentStoryFrequency).optional(),
    })
    .transform((data) => ({
        ...data,
        isPublishing: data.isPublishing ?? false,
    }));

export type UpdateStoryDto = z.infer<typeof updateStoryBodyValidator>;
